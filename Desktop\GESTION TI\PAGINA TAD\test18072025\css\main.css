:root{
  --azul:#003366;
  --naranja:#ff6b00;
  --claro:#e5f1ff;
  --radius:8px;
  --shadow:0 8px 20px rgba(0,0,0,.15);
  --transition:.3s ease;
}
[data-theme="dark"]{
  --bg:#121212;
  --text:#ffffff;
}
body{margin:0;font-family:Inter,Arial,sans-serif;background:var(--bg,#fff);color:var(--text,#1e1e1e);scroll-behavior:smooth;}
nav{position:fixed;top:0;width:100%;display:flex;align-items:center;justify-content:space-between;padding:.75rem 2rem;background:rgba(255,255,255,.8);backdrop-filter:blur(8px);z-index:1000;}
nav ul{display:flex;gap:1.5rem;list-style:none;}
nav a{color:var(--azul);text-decoration:none;font-weight:600;}
.btn-primary{background:var(--naranja);color:#fff;padding:.75rem 1.5rem;border:none;border-radius:var(--radius);cursor:pointer;transition:var(--transition);}
.btn-primary:hover{transform:translateY(-2px);box-shadow:var(--shadow);}
section{padding:4rem 0;}
.section-content{max-width:1200px;margin:0 auto;padding:0 2rem;}
.cards{display:grid;gap:2rem;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));}
.card{background:#fff;border-radius:var(--radius);padding:2rem;box-shadow:var(--shadow);transition:var(--transition);}
.card:hover{transform:translateY(-8px) rotate(1deg);}
.tilt{transition:transform .2s;}
.tilt:hover{transform:perspective(600px) rotateX(5deg) rotateY(-5deg);}
#kpis{display:flex;justify-content:space-around;flex-wrap:wrap;text-align:center;}
.kpi span{font-size:2.5rem;font-weight:700;color:var(--naranja);}
.carousel{display:flex;overflow-x:auto;gap:1rem;}
.carousel img{height:280px;border-radius:var(--radius);}
#mapDemo{position:relative;display:inline-block;border-radius:var(--radius);overflow:hidden;}
.truck-pin{position:absolute;width:20px;height:20px;background:var(--naranja);border-radius:50%;animation:ping 1.5s infinite;}
@media(max-width:600px){
  nav ul{display:none;flex-direction:column;}
  nav ul.open{display:flex;}
}