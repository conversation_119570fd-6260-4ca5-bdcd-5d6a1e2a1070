/* ==========  animations.css  ========== */

/* 1. Fade-in desde abajo al entrar */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeInUp {
  animation: fadeInUp 0.8s ease both;
}

/* 2. Stagger: cada .stagger-* se retrasa */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }

/* 3. Ping infinito para los “truck-pin” en mapa */
@keyframes ping {
  0%   { transform: scale(0.8); opacity: 1; }
  75%  { transform: scale(1.8); opacity: 0; }
  100% { transform: scale(0.8); opacity: 0; }
}

/* 4. Ripple al clickar botones */
@keyframes ripple {
  0%   { transform: scale(0); opacity: 0.6; }
  100% { transform: scale(4); opacity: 0; }
}
.btn-primary,
.btn-secondary {
  position: relative;
  overflow: hidden;
}
.btn-primary::after,
.btn-secondary::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: rgba(255,255,255,0.6);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}
.btn-primary:active::after,
.btn-secondary:active::after {
  animation: none;
}

/* 5. Slide-in para la barra lateral móvil */
@keyframes slideInRight {
  from { transform: translateX(100%); }
  to   { transform: translateX(0); }
}
nav ul {
  animation: slideInRight 0.3s ease;
}

/* 6. Parallax sutil en el hero video */
@keyframes slowZoom {
  from { transform: scale(1); }
  to   { transform: scale(1.05); }
}
#hero video {
  animation: slowZoom 20s ease-in-out infinite alternate;
}