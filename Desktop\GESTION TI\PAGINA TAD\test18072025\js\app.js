/* Dark mode */
const toggle = document.getElementById('darkToggle');
toggle.addEventListener('click', () => {
  document.documentElement.setAttribute(
    'data-theme',
    document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark'
  );
  toggle.textContent = toggle.textContent === '🌙' ? '☀️' : '🌙';
});

/* Menú móvil */
const menuBtn = document.getElementById('menuToggle');
const navUl = document.querySelector('nav ul');
menuBtn.addEventListener('click', () => navUl.classList.toggle('open'));

/* Contadores animados */
const counters = document.querySelectorAll('.counter');
const io = new IntersectionObserver(entries => {
  entries.forEach(e => {
    if (e.isIntersecting) {
      const el = e.target;
      const target = +el.dataset.target;
      const inc = target / 120;
      const tick = () => {
        const val = +el.innerText;
        if (val < target) {
          el.innerText = Math.ceil(val + inc);
          requestAnimationFrame(tick);
        }
      };
      tick();
      io.unobserve(el);
    }
  });
});
counters.forEach(c => io.observe(c));

/* Smooth scroll para anclas */
document.querySelectorAll('a[href^="#"]').forEach(a=>{
  a.addEventListener('click',e=>{
    e.preventDefault();
    document.querySelector(a.getAttribute('href')).scrollIntoView({behavior:'smooth'});
    navUl.classList.remove('open');
  });
});