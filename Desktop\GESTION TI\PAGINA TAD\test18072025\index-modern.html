<!DOCTYPE html>
<html lang="es" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAD – Transporte de Carga y Combustible | Moderno</title>
    <link rel="icon" href="img/favicon.png">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tad-blue': '#003366',
                        'tad-orange': '#ff6b00',
                        'tad-light': '#e5f1ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        .gradient-bg {
            background: linear-gradient(135deg, #003366 0%, #0066cc 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
        }
        
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        .hero-video {
            object-fit: cover;
            filter: brightness(0.7);
        }

        .animate-fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .animation-delay-300 {
            animation-delay: 0.3s;
        }

        .animation-delay-600 {
            animation-delay: 0.6s;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .parallax-bg {
            background-attachment: fixed;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
        }

        .hover-lift {
            transition: all 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 font-sans">
    
    <!-- Navigation -->
    <nav class="fixed top-0 w-full z-50 glass-effect border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <img src="img/TADRCL.png" alt="TAD Logo" class="ml-8 w-auto rounded-lg">
                    <!--comment<span class="ml-3 text-xl font-bold text-white">TAD</span> -->
                </div>
                
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="#servicios" class="text-white hover:text-tad-orange transition-colors duration-300 font-medium">Servicios</a>
                        <a href="#flota" class="text-white hover:text-tad-orange transition-colors duration-300 font-medium">Flota</a>
                        <a href="#clientes" class="text-white hover:text-tad-orange transition-colors duration-300 font-medium">Clientes</a>
                        <a href="#sostenibilidad" class="text-white hover:text-tad-orange transition-colors duration-300 font-medium">Sostenibilidad</a>
                        <a href="#contacto" class="bg-tad-orange hover:bg-orange-600 text-white px-6 py-2 rounded-full transition-all duration-300 transform hover:scale-105">Contacto</a>
                    </div>
                </div>
                
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-white hover:text-tad-orange">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden glass-effect">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#servicios" class="text-white hover:text-tad-orange block px-3 py-2 font-medium">Servicios</a>
                <a href="#flota" class="text-white hover:text-tad-orange block px-3 py-2 font-medium">Flota</a>
                <a href="#clientes" class="text-white hover:text-tad-orange block px-3 py-2 font-medium">Clientes</a>
                <a href="#sostenibilidad" class="text-white hover:text-tad-orange block px-3 py-2 font-medium">Sostenibilidad</a>
                <a href="#contacto" class="text-white hover:text-tad-orange block px-3 py-2 font-medium">Contacto</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="relative h-screen flex items-center justify-center overflow-hidden">
        <video autoplay muted loop playsinline class="absolute inset-0 w-full h-full hero-video">
            <source src="video/tadbase.mp4" type="video/mp4">
        </video>
        
        <div class="absolute inset-0 bg-gradient-to-r from-tad-blue/80 to-transparent"></div>
        
        <div class="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
            <h1 class="text-5xl md:text-7xl font-bold mb-6 animate-fade-in-up">
                Transporte <span class="text-tad-orange">Seguro</span> y Confiable
            </h1>
            <p class="text-xl md:text-2xl mb-8 opacity-90 animate-fade-in-up animation-delay-300">
                Especialistas en combustible y materias peligrosas en todo Chile
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-up animation-delay-600">
                <a href="#contacto" class="bg-tad-orange hover:bg-orange-600 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                    Solicitar Cotización
                </a>
                <a href="#servicios" class="border-2 border-white text-white hover:bg-white hover:text-tad-blue px-8 py-4 rounded-full font-semibold transition-all duration-300">
                    Conocer Servicios
                </a>
            </div>
        </div>
        
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
        </div>
    </section>

    <!-- KPIs Section -->
    <section class="gradient-bg py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
                <div class="animate-float">
                    <div class="text-4xl md:text-5xl font-bold text-tad-orange mb-2" data-counter="450000">0</div>
                    <div class="text-sm md:text-base opacity-90">Km/mes</div>
                </div>
                <div class="animate-float" style="animation-delay: 1s;">
                    <div class="text-4xl md:text-5xl font-bold text-tad-orange mb-2" data-counter="8500">0</div>
                    <div class="text-sm md:text-base opacity-90">Viajes/año</div>
                </div>
                <div class="animate-float" style="animation-delay: 2s;">
                    <div class="text-4xl md:text-5xl font-bold text-tad-orange mb-2" data-counter="720">0</div>
                    <div class="text-sm md:text-base opacity-90">Clientes</div>
                </div>
                <div class="animate-float" style="animation-delay: 3s;">
                    <div class="text-4xl md:text-5xl font-bold text-tad-orange mb-2" data-counter="9000">0</div>
                    <div class="text-sm md:text-base opacity-90">Toneladas/mes</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="servicios" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-tad-blue mb-4">Nuestros Servicios</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Ofrecemos soluciones integrales de transporte especializado con los más altos estándares de seguridad
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="group bg-gradient-to-br from-tad-light to-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                    <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">⛽</div>
                    <h3 class="text-2xl font-bold text-tad-blue mb-4">Transporte de Combustible</h3>
                    <p class="text-gray-600 mb-6">Distribución nacional segura y eficiente con tecnología de vanguardia.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li>✓ Certificación ADR</li>
                        <li>✓ Monitoreo GPS 24/7</li>
                        <li>✓ Válvulas inteligentes</li>
                    </ul>
                </div>
                
                <div class="group bg-gradient-to-br from-orange-50 to-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                    <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">⚠️</div>
                    <h3 class="text-2xl font-bold text-tad-blue mb-4">Materias Peligrosas</h3>
                    <p class="text-gray-600 mb-6">Manejo certificado bajo normativas internacionales ADR y NCh382.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li>✓ Personal certificado</li>
                        <li>✓ Equipos especializados</li>
                        <li>✓ Protocolos de emergencia</li>
                    </ul>
                </div>
                
                <div class="group bg-gradient-to-br from-blue-50 to-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                    <div class="text-6xl mb-6 group-hover:scale-110 transition-transform duration-300">🚛</div>
                    <h3 class="text-2xl font-bold text-tad-blue mb-4">Logística Integral</h3>
                    <p class="text-gray-600 mb-6">Optimización completa de la cadena de suministro y distribución.</p>
                    <ul class="text-sm text-gray-500 space-y-2">
                        <li>✓ Planificación de rutas</li>
                        <li>✓ Gestión de inventarios</li>
                        <li>✓ Reportes en tiempo real</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Fleet Section -->
    <section id="flota" class="py-20 bg-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-tad-blue mb-4">Nuestra Flota</h2>
                <p class="text-xl text-gray-600">Vehículos modernos y especializados para cada tipo de carga</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <img src="img/trucks1.webp" alt="Camión 1" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-tad-blue mb-2">Camiones Cisterna</h3>
                        <p class="text-gray-600">Especializados en transporte de combustibles líquidos</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <img src="img/trucks5.webp" alt="Camión 2" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-tad-blue mb-2">Transporte Especializado</h3>
                        <p class="text-gray-600">Para materias peligrosas y cargas especiales</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <img src="img/trucks6.jpeg" alt="Camión 3" class="w-full h-64 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-tad-blue mb-2">Flota Moderna</h3>
                        <p class="text-gray-600">Tecnología EURO VI para menor impacto ambiental</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Clients Section -->
    <section id="clientes" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-tad-blue mb-4">Nuestros Clientes</h2>
                <p class="text-xl text-gray-600">Empresas líderes que confían en nuestros servicios</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-8 items-center justify-items-center">
                <div class="group">
                    <img src="img/clientes/copec.png" alt="Copec" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/shell.png" alt="Shell" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/mobil.png" alt="Mobil" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/enex.png" alt="Enex" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/aramco.png" alt="Aramco" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/messer.png" alt="Messer" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
                <div class="group">
                    <img src="img/clientes/cabal.png" alt="Cabal" class="h-16 w-auto opacity-60 group-hover:opacity-100 transition-opacity duration-300 filter grayscale group-hover:grayscale-0">
                </div>
            </div>
        </div>
    </section>

    <!-- Sustainability Section -->
    <section id="sostenibilidad" class="py-20 bg-gradient-to-br from-green-50 to-tad-light">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-tad-blue mb-4">Sostenibilidad</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Comprometidos con el medio ambiente a través de tecnología limpia y prácticas responsables
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <div class="space-y-8">
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-tad-blue mb-2">12.450 t</h3>
                                <p class="text-gray-600">CO₂ ahorradas al año con nuestra flota EURO VI</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-tad-blue mb-2">0 L</h3>
                                <p class="text-gray-600">Derramados gracias a válvulas inteligentes y protocolos estrictos</p>
                            </div>
                        </div>

                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-12 h-12 bg-tad-orange rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-tad-blue mb-2">40%</h3>
                                <p class="text-gray-600">De nuestra flota ya cuenta con tecnología EURO VI</p>
                            </div>
                        </div>
                    </div>

                    <div class="mt-8">
                        <a href="#" class="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Descargar Reporte de Sostenibilidad
                        </a>
                    </div>
                </div>

                <div class="relative">
                    <div class="bg-white rounded-2xl p-8 shadow-xl">
                        <h3 class="text-2xl font-bold text-tad-blue mb-6">Plan de Compensación 2025-2030</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Reducción de emisiones</span>
                                <span class="font-semibold text-green-600">-25%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: 25%"></div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Flota EURO VI</span>
                                <span class="font-semibold text-blue-600">40%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 40%"></div>
                            </div>

                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">Energías renovables</span>
                                <span class="font-semibold text-tad-orange">15%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-tad-orange h-2 rounded-full" style="width: 15%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contacto" class="py-20 gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">Contáctanos</h2>
                <p class="text-xl text-white/80 max-w-3xl mx-auto">
                    Estamos listos para atender tus necesidades de transporte especializado
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <div class="space-y-8">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-tad-orange rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">Oficina Central</h3>
                            <p class="text-white/80">Santiago, Chile</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-tad-orange rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">Teléfono</h3>
                            <p class="text-white/80">+56 2 2XXX XXXX</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-tad-orange rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white mb-2">Email</h3>
                            <p class="text-white/80"><EMAIL></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8">
                    <form class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <input type="text" placeholder="Nombre" required
                                       class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-tad-orange focus:border-transparent">
                            </div>
                            <div>
                                <input type="email" placeholder="Email" required
                                       class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-tad-orange focus:border-transparent">
                            </div>
                        </div>

                        <div>
                            <input type="text" placeholder="Empresa"
                                   class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-tad-orange focus:border-transparent">
                        </div>

                        <div>
                            <select class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-tad-orange focus:border-transparent">
                                <option value="">Tipo de servicio</option>
                                <option value="combustible">Transporte de Combustible</option>
                                <option value="peligrosas">Materias Peligrosas</option>
                                <option value="logistica">Logística Integral</option>
                            </select>
                        </div>

                        <div>
                            <textarea placeholder="Mensaje" rows="4" required
                                      class="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-tad-orange focus:border-transparent resize-none"></textarea>
                        </div>

                        <button type="submit"
                                class="w-full bg-tad-orange hover:bg-orange-600 text-white py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                            Enviar Mensaje
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <div class="flex items-center mb-4">
                        <img src="img/logo.jpg" alt="TAD Logo" class="h-12 w-auto rounded-lg">
                        <span class="ml-3 text-2xl font-bold">TAD</span>
                    </div>
                    <p class="text-gray-400 mb-4 max-w-md">
                        Especialistas en transporte de combustible y materias peligrosas con más de 20 años de experiencia en el mercado chileno.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-tad-orange transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-tad-orange transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-tad-orange transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Servicios</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#servicios" class="hover:text-tad-orange transition-colors">Transporte de Combustible</a></li>
                        <li><a href="#servicios" class="hover:text-tad-orange transition-colors">Materias Peligrosas</a></li>
                        <li><a href="#servicios" class="hover:text-tad-orange transition-colors">Logística Integral</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-semibold mb-4">Empresa</h3>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-tad-orange transition-colors">Sobre Nosotros</a></li>
                        <li><a href="#flota" class="hover:text-tad-orange transition-colors">Nuestra Flota</a></li>
                        <li><a href="#sostenibilidad" class="hover:text-tad-orange transition-colors">Sostenibilidad</a></li>
                        <li><a href="#contacto" class="hover:text-tad-orange transition-colors">Contacto</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 TAD Chile – Todos los derechos reservados.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('[data-counter]');
            
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-counter'));
                const duration = 2000;
                const step = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        counter.textContent = target.toLocaleString();
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current).toLocaleString();
                    }
                }, 16);
            });
        }

        // Trigger counter animation when section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(document.querySelector('.gradient-bg'));

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });

                    // Close mobile menu if open
                    const mobileMenu = document.getElementById('mobile-menu');
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('nav');
            if (window.scrollY > 50) {
                navbar.classList.add('bg-white/90');
                navbar.classList.remove('glass-effect');
            } else {
                navbar.classList.remove('bg-white/90');
                navbar.classList.add('glass-effect');
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const fadeInObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements for fade-in animation
        document.querySelectorAll('section h2, .card, .group').forEach(el => {
            fadeInObserver.observe(el);
        });

        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const hero = document.getElementById('hero');
            const video = hero.querySelector('video');

            if (video && scrolled < hero.offsetHeight) {
                video.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Form submission
        document.querySelector('#contacto form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Simple form validation and submission simulation
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.textContent;

            button.textContent = 'Enviando...';
            button.disabled = true;

            setTimeout(() => {
                button.textContent = 'Mensaje Enviado ✓';
                button.classList.add('bg-green-600');
                button.classList.remove('bg-tad-orange');

                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                    button.classList.remove('bg-green-600');
                    button.classList.add('bg-tad-orange');
                    this.reset();
                }, 2000);
            }, 1500);
        });
    </script>
</body>
</html>
